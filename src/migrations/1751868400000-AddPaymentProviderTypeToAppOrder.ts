import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPaymentProviderTypeToAppOrder1751868400000
  implements MigrationInterface
{
  name = 'AddPaymentProviderTypeToAppOrder1751868400000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE \`app_order\`
      ADD \`paymentProviderType\` ENUM('AUTOBANK', 'PAYOS', 'OTHER')
      NOT NULL DEFAULT 'OTHER'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE \`app_order\`
      DROP COLUMN \`paymentProviderType\`
    `);
  }
}
