# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Client {
  id: String!
  name: String!
  ownerName: String
  email: String!
  password: String!
  phone: String!
  province: String!
  address: String!
  createdAt: String!
  createdBy: String!
  updatedAt: String
  updatedBy: String
  totalOrders: Float!
  totalMachines: Float!
}

type WaitingScreenImage {
  id: String!
  url: String!
  waitingScreen: WaitingScreen!
  createdAt: DateTime!
  updatedAt: DateTime
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

type WaitingScreen {
  id: String!
  name: String
  clientId: String!
  images: [WaitingScreenImage!]!
  machineIds: String
  type: String!
  createdAt: String!
  updatedAt: String
}

type PromotionStatistics {
  totalOrders: Float!
  totalRevenue: Float!
}

type Promotion {
  id: String!
  clientId: String!
  name: String!
  machineIds: String
  startDate: String!
  endDate: String!
  usageLimitPerCode: Float!
  maxPromotionCodes: Float!
  discountValue: Float!
  promotionCode: String!
  createdAt: String!
  updatedAt: String!
}

type PromotionResponse {
  id: String!
  name: String
  machineIds: String
  startDate: String
  endDate: String
  usageLimitPerCode: Float
  maxPromotionCodes: Float
  discountValue: Float
  promotionCode: String
  createdAt: String
  updatedAt: String
  codes: [PromotionCode!]!
  statistics: PromotionStatistics
}

type ExportPromotionResponse {
  url: String!
}

type PromotionCode {
  id: String!
  promotionId: String
  promotion: Promotion
  code: String
  isUsed: Boolean
  numberUsed: Float
  createdAt: String
  updatedAt: String
}

type FrameItem {
  id: String!
  frameId: String!
  itemType: String!
  itemId: String
  x_coordinate: Float!
  parentId: String
  y_coordinate: Float!
  width: Float!
  height: Float!
  position: Float!
  angle: Float
  createdAt: String!
  updatedAt: String
  frame: Frame!
}

type Layout {
  id: String!
  name: String
  clientId: String!
  machineIds: String
  status: String!
  layoutType: String!
  createdAt: String!
  updatedAt: String
  formats: [LayoutFormat!]
}

type LayoutFormat {
  id: String!
  layoutId: String
  name: String!
  imageCount: Float!
  status: String
  createdAt: String!
  updatedAt: String
  layout: Layout
  layoutItems: [LayoutItem!]!
}

type LayoutItem {
  id: String!
  layoutFormatId: String!
  imageUrl: String
  topicId: String
  position: Float
  createdAt: String!
  updatedAt: String
  layoutFormat: LayoutFormat
  topic: Topic
}

type Topic {
  id: ID!
  name: String!
  extraFee: Float!
  isActive: Boolean!
  clientId: String!
  createdAt: DateTime!
  updatedAt: DateTime
  totalFrame: Float
}

type Frame {
  id: String!
  clientId: String
  description: String
  imageUrl: String
  frameSize: String
  numberImage: Float
  numberPicture: Float
  topicId: String
  orientation: String
  dateColor: String
  createdAt: String
  updatedAt: String
  deletedAt: String
  frameItems: [FrameItem!]
}

type MachineBrand {
  id: String!
  brandCode: String!
  name: String!
  description: String!
  supported: Boolean!
  code: String!
  modelKey: String
  createdAt: String!
  createdBy: String!
  updatedAt: String
  updatedBy: String
  machines: [Machine!]!
}

type MachineStatus {
  id: String!
  machineId: String
  isUpdated: Boolean
  syncDate: String
  clientId: String
  status: String
  createdAt: DateTime
  updatedAt: DateTime
  machine: Machine
}

type Machine {
  id: String!
  price: Float!
  machineId: String!
  machineCode: String!
  machinePin: String!
  remotePin: String
  userId: String
  orderId: String
  rentalDate: String
  renewalDate: String
  havePayScreen: Boolean
  productionStatus: String
  status: String
  expiryDate: String!
  location: String
  lastPingAt: String
  pendingPrints: Float
  remainingMedia: Float
  remainingInk: Float
  state: String
  hasPayment: Boolean
  description: String
  createdAt: String!
  createdBy: String!
  updatedAt: String
  updatedBy: String
  machineBrand: MachineBrand!
  orders: [app_order!]
}

type MachineResponse {
  id: String!
  price: Float!
  machineBrand: MachineBrand
  machineCode: String!
  machinePin: String!
  remotePin: String
  user: Client
  orderId: String
  rentalDate: String
  renewalDate: String
  havePayScreen: Boolean
  productionStatus: String
  status: String
  expiryDate: String!
  lastPingAt: String
  pendingPrints: Float
  remainingMedia: Float
  remainingInk: Float
  state: String
  hasPayment: Boolean
  description: String
  location: String
  createdAt: String!
  createdBy: String!
  updatedAt: String
  updatedBy: String
}

type AppImage {
  id: ID!
  fileUrl: String!
  fileName: String!
  fileType: String
  orderId: ID
  createdAt: DateTime!
  updatedAt: DateTime!
}

type app_order {
  id: String!
  orderCode: String
  amount: Float
  totalOrderNumber: Float
  receivedAmount: Float
  description: String
  refCode: String
  imageNumber: Float
  status: String
  paymentMethod: String
  paymentProviderType: String
  captureMode: String
  machineId: String
  clientId: String
  settingSizeId: String
  promotionId: String
  promotionCodeId: String
  topicId: String
  frameId: String
  denominations: String
  createdAt: DateTime
  promotionCode: PromotionCode
  frame: Frame
  topic: Topic
  settingSize: SettingSize
  machine: Machine
  client: Client
  images: [AppImage!]
}

type SettingSize {
  id: String!
  name: String
  settingSizeType: String!
  machineIds: String
  clientId: String!
  smallSizePrice2: Int
  smallSizePrice4: Int
  smallSizePrice6: Int
  smallSizePrice8: Int
  smallSizePrice10: Int
  largeSizePrice2: Int
  largeSizePrice3: Int
  largeSizePrice4: Int
  largeSizePrice5: Int
  largeSizePrice6: Int
  status: String
  createdAt: DateTime!
  updatedAt: DateTime
}

type MBank {
  id: ID!
  code: String!
  shortName: String
  name: String!
  logo: String
  paymentAccountSetting: [PaymentAccountSetting!]
}

type PaymentAccountSetting {
  id: ID!
  bankName: String!
  apiId: String
  apiKey: String
  checkSum: String
  mBankId: String
  bankOwnerName: String
  bankAccountNumber: String
  isActive: Boolean!
  name: String!
  type: String
  clientId: String!
  createdAt: DateTime!
  updatedAt: DateTime!
  mBank: MBank
}

type Sticker {
  id: String!
  clientId: String!
  image: String
  createdAt: DateTime!
  updatedAt: DateTime
}

type PrintSetting {
  id: ID!
  clientId: String!
  machineId: String
  type: String!
  adjustColor: Float
  printRetry: Float
  border: Float
  sharpness: Float
  overcoatFinish: Float
  printerQuality: Float
  yResolution: Float
  adjGammaR: Float
  adjGammaG: Float
  adjGammaB: Float
  adjBrightnessR: Float
  adjBrightnessG: Float
  adjBrightnessB: Float
  adjContrastR: Float
  adjContrastG: Float
  adjContrastB: Float
  adjChroma: Float
  icmMethod: Float
  createdAt: String!
  updatedAt: String
}

type AppearanceSetting {
  id: String!
  logo: String
  background: String
  primary_color: String!
  secondary_color: String!
  background_color: String!
  primary_text_color: String!
  secondary_text_color: String!
  secondary_text_color_2: String!
  clientId: String!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type ClientAccount {
  id: String!
  clientId: String!
  name: String!
  email: String!
  password: String!
  role: String!
  createdAt: String!
  updatedAt: String
}

type AdminClientResponse {
  id: String!
  name: String!
  email: String!
  ownerName: String
  phone: String!
  province: String!
  address: String!
  totalOrders: Float!
  totalMachines: Float!
  createdAt: String!
  isPayOn: Boolean
}

type ClientGenerateFramesResponse {
  isSuccess: Boolean!
}

type Auth {
  id: String!
  email: String!
  token: String!
  createdAt: String!
  role: String
}

type User {
  id: String!
  name: String!
  email: String!
  password: String!
  role: String!
  createdAt: String!
  updatedAt: String
}

type Order {
  id: String!
  orderId: String!
  userId: String!
  paymentIds: Float
  machineIds: String!
  discount: Float!
  totalAmount: Float!
  advancePayment: Float!
  appointedDate: String!
  status: String!
  editContent: Float
  createdAt: String!
  createdBy: String!
  updatedAt: String
  updatedBy: String
}

type OrderResponse {
  id: String!
  orderId: String!
  user: Client!
  paymentIds: String
  machines: [MachineResponse!]!
  discount: Float!
  totalAmount: Float!
  advancePayment: Float!
  appointedDate: String!
  status: String!
  editContent: String
}

type AdminClientAccountResponse {
  id: String!
  clientId: String!
  name: String!
  email: String!
  createdAt: String!
}

type AdminAnalyticsResponse {
  totalReceivedAmount: Float!
  totalReceivedAmountPrevious: Float!
  growthPercentage: Float!
  totalBankTranfer: Float!
  totalCash: Float!
  totalOrders: Float!
  totalPreviousOrders: Float!
  growthOrders: Float!
}

type AdminOrderObjectType {
  id: ID!
  orderCode: String
  amount: Float
  totalOrderNumber: Float
  receivedAmount: Float
  description: String
  refCode: String
  imageNumber: Float
  status: String
  paymentMethod: String
  captureMode: String
  machineId: String
  clientId: String
  settingSizeId: String
  promotionId: String
  promotionCodeId: String
  topicId: String
  frameId: String
  createdAt: DateTime
  updatedAt: DateTime
  promotionCode: PromotionCode
  frame: Frame
  topic: Topic
  settingSize: SettingSize
  machine: Machine
  images: [AppImage!]
  domain: String
}

type AdminAnalyticsOrdersDataResponse {
  data: [AdminOrderObjectType!]!
  total: Float!
  currentPage: Float!
  pageSize: Float!
  totalOrderNumber: Float!
  totalRecievedAmount: Float!
  totalAmount: Float!
}

type AdminRevenueChart {
  time: String
  value: Float
  revenue: Float
}

type AdminRevenueChartResponse {
  current: [AdminRevenueChart!]!
  previous: [AdminRevenueChart!]!
}

type AdminUploadResponse {
  message: String!
  domain: String!
}

type AdminCreateTimeLapseResponse {
  isSuccess: Boolean!
}

type AdminReupImageResponse {
  message: String!
}

type ClientInfo {
  id: String!
  name: String!
  ownerName: String
}

type MachineInfo {
  id: String!
  machineCode: String!
  location: String
  machineName: String
}

type TopicInfo {
  id: String!
  name: String!
  extraFee: Float
}

type PromotionInfo {
  id: String
  name: String
  code: String
  discountValue: Float
}

type AppOrderSummary {
  totalOrders: Float!
  totalRevenue: Float!
  totalReceivedAmount: Float!
  totalDiscountAmount: Float!
}

type ImageStatusInfo {
  hasVideo: Boolean!
  hasMergedImage: Boolean!
  hasRegularImages: Boolean!
  totalImages: Float!
  videoCount: Float!
  mergedImageCount: Float!
  regularImageCount: Float!
  isComplete: Boolean!
}

type AppOrderDetailResponse {
  id: String!
  orderCode: String
  amount: Float
  totalOrderNumber: Float
  receivedAmount: Float
  description: String
  refCode: String
  imageNumber: Float
  status: String
  paymentMethod: String
  captureMode: String
  createdAt: DateTime
  denominations: String
  discountAmount: Float
  client: ClientInfo
  machine: MachineInfo
  topic: TopicInfo
  promotion: PromotionInfo
  finalImageUrl: String
  imageDomain: String
  clientName: String
  imageStatus: ImageStatusInfo
}

type AppOrderListResponse {
  orders: [AppOrderDetailResponse!]!
  total: Int!
  page: Int!
  limit: Int!
  totalPages: Int!
  summary: AppOrderSummary!
}

type ClientCurrentSessionResponse {
  clientId: String!
  email: String!
  role: String!
}

type WaitingScreenResponse {
  id: String!
  name: String
  machineIds: String
  type: String
  images: [ImageResponse]
}

type ImageResponse {
  id: String!
  url: String
}

type UploadBackgroundImageResponse {
  id: String!
  fileName: String!
  fileUrl: String!
  description: String
  message: String!
}

type BackgroundImageResponse {
  id: String!
  fileName: String!
  fileUrl: String!
  description: String
  createdAt: DateTime!
  updatedAt: DateTime!
}

type DeleteBackgroundImageResponse {
  success: Boolean!
  message: String!
}

type BackgroundImageListResponse {
  images: [BackgroundImageResponse!]!
  total: Float!
  message: String!
}

type FrameUsageStats {
  frame: Frame!
  usedCount: Float!
}

type FrameStatisticResponse {
  mostUsed: [FrameUsageStats!]!
  leastUsed: [FrameUsageStats!]!
}

type PromotionOrderResponse {
  id: String!
  orderCode: String
  amount: Float
  totalOrderNumber: Float
  receivedAmount: Float
  description: String
  refCode: String
  imageNumber: Float
  status: String
  paymentMethod: String
  captureMode: String
  createdAt: DateTime
  denominations: String
  domain: String
  promotionCodeUsed: String
  machineName: String
  machineCode: String
  machineLocation: String
  machineId: String
  frameName: String
  frameDescription: String
  frameImageUrl: String
  frameSize: String
  topicName: String
  topicExtraFee: Float
  settingSizeName: String
  promotionName: String
  promotionDiscount: Float
  imageCount: Float
  finalImageUrl: String
}

type PromotionOrdersResponse {
  orders: [PromotionOrderResponse!]!
  total: Int!
  page: Int!
  limit: Int!
  totalPages: Int!
}

type AnalyticsResponse {
  totalReceivedAmount: Float!
  totalReceivedAmountPrevious: Float!
  growthPercentage: Float!
  totalBankTranfer: Float!
  totalCash: Float!
  totalOrders: Float!
  totalPreviousOrders: Float!
  growthOrders: Float!
}

type OrderObjectType {
  id: ID!
  orderCode: String
  amount: Float
  totalOrderNumber: Float
  receivedAmount: Float
  description: String
  refCode: String
  imageNumber: Float
  status: String
  paymentMethod: String
  captureMode: String
  machineId: String
  clientId: String
  settingSizeId: String
  promotionId: String
  promotionCodeId: String
  topicId: String
  frameId: String
  createdAt: DateTime
  updatedAt: DateTime
  promotionCode: PromotionCode
  frame: Frame
  topic: Topic
  settingSize: SettingSize
  machine: Machine
  images: [AppImage!]
  domain: String
}

type AnalyticsOrdersDataResponse {
  data: [OrderObjectType!]!
  total: Float!
  currentPage: Float!
  pageSize: Float!
  totalOrderNumber: Float!
  totalRecievedAmount: Float!
  totalAmount: Float!
}

type RevenueChart {
  time: String
  value: Float
  revenue: Float
}

type RevenueChartResponse {
  current: [RevenueChart!]!
  previous: [RevenueChart!]!
}

type TopTopicsResponse {
  id: String
  name: String
  total: Float
}

type TopFramesResponse {
  id: String
  description: String
  frameImage: String
  total: Float
}

type TopMachinesResponse {
  id: String
  total: Float
  location: String
  machineCode: String
}

type ExportOrdersResponse {
  success: Boolean!
  message: String!
  fileName: String!
  fileData: String!
}

type PrintImageResponse {
  message: String!
}

type PrintSettingResponse {
  id: String!
  machineId: String
  type: String
  adjustColor: Float
  printRetry: Float
  border: Float
  sharpness: Float
  overcoatFinish: Float
  printerQuality: Float
  yResolution: Float
  adjGammaR: Float
  adjGammaG: Float
  adjGammaB: Float
  adjBrightnessR: Float
  adjBrightnessG: Float
  adjBrightnessB: Float
  adjContrastR: Float
  adjContrastG: Float
  adjContrastB: Float
  adjChroma: Float
  icmMethod: Float
  createdAt: String
  updatedAt: String
}

type ClientClientAccountResponse {
  id: String!
  role: String!
  clientId: String!
  name: String!
  email: String!
  createdAt: String!
}

type ReupImageResponse {
  message: String!
}

type CostReconciliationResponse {
  id: String!
  clientId: String!
  clientName: String!
  revenue: Float!
  discountAmount: Float!
  status: String!
  reconciliationDate: DateTime!
  createdAt: DateTime!
  updatedAt: DateTime!
  createdBy: String
  updatedBy: String
  notes: String
  totalOrders: Int!
  processedOrderIds: String
  reconciliationCode: String!
}

type CostReconciliationListResponse {
  data: [CostReconciliationResponse!]!
  total: Int!
  page: Int!
  limit: Int!
  totalPages: Int!
}

type ReconciliationCodeResponse {
  refCode: String!
  qrCodeUrl: String
  month: String!
  clientId: String!
  clientName: String!
  revenue: Float!
  discountAmount: Float!
  totalOrders: Float!
  status: String!
  generatedAt: DateTime!
}

type GenerateReconciliationCodeResponse {
  refCode: String!
  month: String!
  amount: Float!
  generatedAt: String!
}

type AppUser {
  id: ID!
  machineId: String!
  token: String!
  code: String!
}

type AppWaitingScreenResponse {
  id: String!
  name: String
  machineIds: String
  type: String
  images: [AppImageResponse!]
}

type AppImageResponse {
  id: String!
  url: String!
}

type AttemptPaymentResponse {
  qrCode: String
  refCode: String
  skipPayment: Boolean
  qrCodeUrl: String
}

type AttemptPaymentCashResponse {
  orderId: String!
  domain: String!
}

type AppPromotionResponse {
  id: String!
  name: String
  promotionCode: String
  canUse: Boolean!
  discountValue: Float!
}

type ClientAppUpdateMachineResponse {
  success: Boolean!
}

type AppearanceSettingResponse {
  id: ID!
  primary_color: String
  secondary_color: String
  background_color: String
  primary_text_color: String
  secondary_text_color: String
  secondary_text_color_2: String
  logo: String
  background: String
  havePayScreen: Boolean
  clientId: String
  createdAt: DateTime
  updatedAt: DateTime
}

type ClientAppBackgroundImageDto {
  id: String!
  fileName: String!
  fileUrl: String!
  description: String
  createdAt: DateTime!
  updatedAt: DateTime!
}

type ClientAppBackgroundImageListResponse {
  images: [ClientAppBackgroundImageDto!]!
  total: Int!
  message: String!
}

type LogResponse {
  """Status of the log operation"""
  status: String!

  """Message about the log operation"""
  message: String!
}

type CreateImageResponse {
  message: String!
  captureMode: String!
  domain: String!
}

type CreateTimeLapseResponse {
  isSuccess: Boolean!
}

type JobStatusResponse {
  jobId: String!
  status: String!
  message: String!
  progress: Float!
  result: String
  error: String
}

type UploadResponse {
  message: String!
}

type ClientAppPrintImageResponse {
  message: String!
}

type AppPrintSettingResponse {
  id: String!
  machineIds: String
  type: String
  adjustColor: Float
  printRetry: Float
  border: Float
  sharpness: Float
  overcoatFinish: Float
  printerQuality: Float
  yResolution: Float
  adjGammaR: Float
  adjGammaG: Float
  adjGammaB: Float
  adjBrightnessR: Float
  adjBrightnessG: Float
  adjBrightnessB: Float
  adjContrastR: Float
  adjContrastG: Float
  adjContrastB: Float
  adjChroma: Float
  icmMethod: Float
  createdAt: String
  updatedAt: String
}

type ClientAppProfileClientInfo {
  id: String!
  name: String!
  ownerName: String
  email: String!
  phone: String
  address: String
}

type ClientAppProfileMachineInfo {
  id: String!
  machineCode: String!
  location: String
  description: String
  pendingPrints: Float
  remainingMedia: Float
  remainingInk: Float
  state: String
  lastPingAt: String
  createdAt: DateTime
}

type ClientAppProfileResponse {
  machine: ClientAppProfileMachineInfo!
  client: ClientAppProfileClientInfo!
}

type ImageRes {
  id: ID!
  fileUrl: String!
  fileName: String!
  fileType: String
  orderId: String
  clientName: String
  appearanceSetting: AppearanceSetting
  createdAt: String!
  updatedAt: String!
  orderCreatedAt: String
  message: String
}

type Query {
  adminClientRevenue(clientId: String!, startDate: String!, endDate: String!): AdminAnalyticsResponse!
  adminClientAnalyticsOrders(clientId: String!, startDate: String!, endDate: String!, paramsInput: AdminParamsInput!): AdminAnalyticsOrdersDataResponse!
  adminClientGetRevenueChart(clientId: String!, startDate: String!, endDate: String!): AdminRevenueChartResponse!
  adminMachineById(id: ID!): MachineResponse!
  adminClientById(id: ID!): Client!
  adminClientAccountById(id: ID!, clientId: ID!): ClientAccount!
  adminMachineBrands(input: GetMachineBrandInput!): [MachineBrand!]!
  adminMachineBrandById(id: ID!): MachineBrand!
  adminOrderById(id: ID!): OrderResponse!
  adminUsers: [User!]!
  adminUserById(id: ID!): User!
  adminSignOut: Auth!
  adminGetPaymentAccountSettings(clientId: String!): [PaymentAccountSetting!]!
  adminGetPaymentAccountSettingById(id: ID!): PaymentAccountSetting
  adminMBanks: [MBank!]!
  adminGetAppOrderById(id: String!): AppOrderDetailResponse
  adminGetAppOrderList(input: AppOrderListInput!): AppOrderListResponse!
  clientSignOut: Auth!
  clientCurrentSession: ClientCurrentSessionResponse!
  clientClientAccountById(id: ID!): ClientAccount!
  clientMachineById(id: ID!): MachineResponse!
  clientWaitingScreens(input: GetWaitingScreenInput!): [WaitingScreenResponse!]!
  clientWaitingScreenById(id: ID!): WaitingScreenResponse!
  clientGetTopics: [Topic!]!
  clientGetTopic(id: String!): Topic!
  clientGetSettingSizes: [SettingSize!]!
  clientGetSettingSize(id: String!): SettingSize!
  clientGetAppearanceSetting: AppearanceSetting
  clientGetBackgroundImages: BackgroundImageListResponse!
  clientGetBackgroundImage(id: String!): BackgroundImageResponse
  clientGetLayouts: [Layout!]!
  clientGetLayout(id: String!): Layout!
  clientGetFrames(topicID: String, frameSize: String): [Frame!]!
  clientGetFrame(id: String!): Frame!
  clientFrameUsedStatistic: FrameStatisticResponse!
  clientGetFrameItems(frameId: String!): [FrameItem!]!
  clientGetLayoutItemsByFormatId(formatId: String!): [LayoutItem!]!
  clientGetPromotions(status: String!): [Promotion!]!
  clientGetPromotion(id: String!): PromotionResponse!
  clientExportPromotion(id: String!): ExportPromotionResponse!
  clientGetPromotionOrders(input: PromotionOrdersInput!): PromotionOrdersResponse!
  clientGetStickers: [Sticker!]!
  clientRevenue(startDate: String!, endDate: String!, machineIds: [String!]): AnalyticsResponse!
  clientAnalyticsOrders(startDate: String!, endDate: String!, paramsInput: ParamsInput!): AnalyticsOrdersDataResponse!
  clientTopTopics(startDate: String!, endDate: String!, machineIds: [String!]): [TopTopicsResponse!]!
  clientTopFrames(startDate: String!, endDate: String!, machineIds: [String!]): [TopFramesResponse!]!
  topMachines(startDate: String!, endDate: String!, machineIds: [String!]): [TopMachinesResponse!]!
  clienGetRevenueChart(startDate: String!, endDate: String!): RevenueChartResponse!
  clientExportOrders(startDate: String!, endDate: String!, machineIds: [String!], fileType: String = "csv"): ExportOrdersResponse!
  clientGetMachineStatuses: [MachineStatus!]!
  clientPrintSettings: [PrintSettingResponse!]!
  clientPrintSettingById(id: ID!): PrintSettingResponse!
  clientCostReconciliations(input: GetCostReconciliationInput!): CostReconciliationListResponse!
  getReconciliationCodeByMonth(input: GetReconciliationCodeByMonthInput!): ReconciliationCodeResponse
  clientAppWaitingScreen: AppWaitingScreenResponse
  clientAppGetTopics: [Topic!]!
  clientAppGetTopic(id: String!): Topic!
  clientAppGetLayouts: [Layout!]!
  clientAppGetSettingSizes: [SettingSize!]!
  clientAppGetSettingSize(id: String!): SettingSize!
  clientAppGetFrames: [Frame!]
  clientAppGetFrame(id: String!): Frame
  clientAppGetStickers: [Sticker!]!
  clientAppCheckPromotion(promotionCode: String!): AppPromotionResponse!
  clientAppGetAppearanceSetting: AppearanceSettingResponse
  clientAppGetBackgroundImages(input: ClientAppBackgroundImageListInput): ClientAppBackgroundImageListResponse!
  getJobStatus(jobId: String!): JobStatusResponse!
  checkQueueStatus: String!
  clientAppPrintSetting: AppPrintSettingResponse!
  clientAppGetProfile: ClientAppProfileResponse!
  clientMemoryGetImages(orderId: String!): [ImageRes!]!
}

input AdminParamsInput {
  paymentType: String
  frameIds: [String!]
  machineIds: [String!]
  topicIds: [String!]
  page: Float! = 1
  limit: Float! = 10
}

input GetMachineBrandInput {
  name: String
  supported: Boolean
}

input AppOrderListInput {
  search: String
  clientId: String
  startDate: String
  endDate: String
  page: Int = 1
  limit: Int = 10
}

input GetWaitingScreenInput {
  machineId: String
}

input PromotionOrdersInput {
  promotionId: String!
  page: Int = 1
  limit: Int = 10
}

input ParamsInput {
  paymentType: String
  frameIds: [String!]
  machineIds: [String!]
  topicIds: [String!]
  page: Float! = 1
  limit: Float! = 10
}

input GetCostReconciliationInput {
  clientId: String
  status: String
  startDate: Float
  endDate: Float
  page: Int = 1
  limit: Int = 10
}

input GetReconciliationCodeByMonthInput {
  month: String!
  clientId: String
}

input ClientAppBackgroundImageListInput {
  _dummy: String
}

type Mutation {
  adminMachines(input: GetMachineInput!): [MachineResponse!]!
  adminCreateMachine(input: CreateMachineInput!): MachineResponse!
  adminUpdateMachine(input: UpdateMachineInput!): MachineResponse!
  adminDeleteMachine(id: ID!): Machine!
  adminClients(input: GetClientInput!): [AdminClientResponse!]!
  adminCreateClient(input: CreateClientInput!): Client!
  adminUpdateClient(input: UpdateClientInput!): Client!
  adminDeleteClient(id: ID!): Client!
  adminClientGenerateFrames(clientId: ID!): ClientGenerateFramesResponse!
  adminLoginClientById(id: ID!): Auth!
  adminClientAccounts(input: AdminGetClientAccountInput!, clientId: ID!): [AdminClientAccountResponse!]!
  adminCreateClientAccount(input: AdminCreateClientAccountInput!, clientId: ID!): ClientAccount!
  adminUpdateClientAccount(input: AdminUpdateClientAccountInput!, clientId: ID!): ClientAccount!
  adminDeleteClientAccount(id: ID!, clientId: ID!): ClientAccount!
  adminCreateMachineBrand(input: CreateMachineBrandInput!): MachineBrand!
  adminUpdateMachineBrand(input: UpdateMachineBrandInput!): MachineBrand!
  adminDeleteMachineBrand(id: ID!): MachineBrand!
  adminOrders(input: GetOrderInput!): [OrderResponse!]!
  adminCreateOrder(input: CreateOrderInput!): OrderResponse!
  adminUpdateOrder(input: UpdateOrderInput!): OrderResponse!
  adminDeleteOrder(id: ID!): Order!
  adminCreateUser(input: CreateUserInput!): User!
  adminUpdateUser(input: UpdateUserInput!): User!
  adminDeleteUser(id: ID!): User!
  adminSignIn(input: SignInInput!): Auth!
  adminCreatePaymentAccountSetting(input: CreatePaymentAccountSettingInput!): PaymentAccountSetting!
  adminUpdatePaymentAccountSetting(input: UpdatePaymentAccountSettingInput!): PaymentAccountSetting!
  adminDeletePaymentAccountSetting(id: ID!): Boolean!
  adminCreateImage(input: AdminCreateImageInput!, file: Upload!): AdminUploadResponse!
  adminCreateTimeLapse(input: AdminCreateImageInput!, file: Upload!): AdminCreateTimeLapseResponse!
  adminReupImage(input: AdminReupImageInput!): AdminReupImageResponse!
  clientSignIn(input: ClientSignInInput!): Auth!
  clientClientAccounts(input: GetClientAccountInput!): [ClientClientAccountResponse!]!
  clientCreateClientAccount(input: CreateClientAccountInput!): ClientAccount!
  clientUpdateClientAccount(input: UpdateClientAccountInput!): ClientAccount!
  clientDeleteClientAccount(id: ID!): ClientAccount!
  clientMachines(input: ClientGetMachineInput!): [MachineResponse!]!
  clientUpdateMachine(input: ClientUpdateMachineInput!): MachineResponse!
  clientCreateWaitingScreen(input: CreateWaitingScreenInput!, files: [Upload!]!): WaitingScreenResponse!
  clientUpdateWaitingScreen(input: UpdateWaitingScreenInput!, files: [Upload]): WaitingScreenResponse!
  clientDeleteWaitingScreen(id: ID!): PrintSetting!
  clientCreateTopic(input: CreateTopicInput!): Topic!
  clientUpdateTopic(input: UpdateTopicInput!): Topic!
  clientDeleteTopic(id: String!): Topic!
  clientCreateSettingSize(input: CreateSettingSizeInput!): SettingSize!
  clientUpdateSettingSize(input: UpdateSettingSizeInput!): SettingSize!
  clientDeleteSettingSize(id: String!): SettingSize!
  clientCreateAppearanceSetting(input: AppearanceSettingInput!, logo: Upload, background: Upload): AppearanceSetting!
  clientUpdateAppearanceSetting(input: UpdateAppearanceSettingInput!, logo: Upload, background: Upload): AppearanceSetting!
  clientDeleteAppearanceSetting(id: String!): Boolean!
  clientUploadBackgroundImage(input: UploadBackgroundImageInput!, image: Upload!): UploadBackgroundImageResponse!
  clientDeleteBackgroundImage(id: String!): DeleteBackgroundImageResponse!
  clientCreateLayout(input: CreateLayoutInput!): Layout!
  clientUpdateLayout(input: UpdateLayoutInput!): Layout!
  clientDeleteLayout(id: String!): Layout!
  clientSetDefaultFormat(layoutId: String!, defaultFormatId: String!): Layout!
  clientCreateFrame(input: FrameInput!, image: Upload): Frame!
  clientUpdateFrame(input: UpdateFrameInput!, image: Upload): Frame!
  clientDeleteFrame(id: String!): Frame!
  clientCreateFrameItem(input: FrameItemInput!): FrameItem!
  clientUpdateFrameItem(input: UpdateFrameItemInput!): FrameItem!
  clientDeleteFrameItem(id: String!): Boolean!
  clientUpdateLayoutItem(input: UpdateLayoutItemInput!, image: Upload): LayoutItem!
  clientCreatePromotion(createPromotionInput: CreatePromotionInput!): Promotion!
  clientDeletePromotion(id: String!): Promotion!
  clientFinishPromotion(id: String!): Promotion!
  clientCreateSticker(image: Upload!): Sticker!
  clientDeleteSticker(id: String!): Sticker!
  clientUpdateMachineStatus(machineId: String!): Boolean!
  clientPrintImage(input: PrintImageInput!): PrintImageResponse!
  clientCreatePrintSetting(input: CreatePrintSettingInput!): PrintSettingResponse!
  clientUpdatePrintSetting(input: UpdatePrintSettingInput!): PrintSettingResponse!
  clientReupImage(input: ReupImageInput!): ReupImageResponse!
  generateCostReconciliationCode(input: GenerateReconciliationCodeInput!): GenerateReconciliationCodeResponse!
  generateCurrentMonthReconciliationCode(amount: Float!): GenerateReconciliationCodeResponse!
  clientAppSignOut: Auth!
  clientAppSignInByCode(input: SignInByCodeInput!): AppUser!
  clientAppCreateOrderPayOnline(input: AppOrderOnlineInput!): AttemptPaymentResponse!
  clientAppCreateOrderPayWithCash(input: AppOrderOnlineCashInput!): AttemptPaymentCashResponse!
  clientAppUpdateLastPing: Boolean!
  clientAppUpdateMachine(input: ClientAppUpdateMachineInput!): ClientAppUpdateMachineResponse!
  clientAppLogPublisher(input: LogPublisherInput!): LogResponse!
  clientApplogWithoutAuth(input: LogPublisherInput!): LogResponse!
  clientAppCreateImage(input: CreateImageInput!, file: Upload!): CreateImageResponse!
  clientAppCreateTimeLapse(input: CreateImageInput!, file: Upload!): CreateTimeLapseResponse!
  clientAppUploadOfflineLog(file: Upload!): UploadResponse!
  clientAppUpdateMachineStatus(status: String!): Boolean!
  clientAppPrintImage: ClientAppPrintImageResponse!
}

input GetMachineInput {
  machineId: String
  machineCode: String
  productionStatus: String
  havePayScreen: Boolean
  haveOrder: Boolean
  userId: String
  expiryDate: String
  description: String
}

input CreateMachineInput {
  machineId: String!
  price: Float!
  productionStatus: String
  havePayScreen: Boolean
  expiryDate: String!
  description: String
}

input UpdateMachineInput {
  id: String!
  machineId: String
  price: Float
  productionStatus: String
  havePayScreen: Boolean
  renewalDate: String
  orderId: String
  userId: String
  expiryDate: String
  description: String
  remotePin: String
}

input GetClientInput {
  name: String
  ownerName: String
  email: String
  phone: String
  province: String
  address: String
}

input CreateClientInput {
  name: String!
  ownerName: String
  email: String!
  password: String!
  phone: String!
  province: String!
  address: String!
}

input UpdateClientInput {
  id: String!
  name: String
  ownerName: String
  password: String
  phone: String
  province: String
  address: String
  email: String
}

input AdminGetClientAccountInput {
  name: String
  email: String
}

input AdminCreateClientAccountInput {
  name: String!
  email: String!
  password: String!
}

input AdminUpdateClientAccountInput {
  id: String!
  name: String
  password: String
  email: String
}

input CreateMachineBrandInput {
  name: String!
  description: String
  supported: Boolean
  code: String
  modelKey: String
}

input UpdateMachineBrandInput {
  id: String!
  name: String
  description: String
  supported: Boolean
  code: String
  modelKey: String
}

input GetOrderInput {
  orderId: String
  status: String
  userId: String
}

input CreateOrderInput {
  userId: String!
  appointedDate: String!
  machineIds: String!
  discount: Float!
}

input UpdateOrderInput {
  id: String!
  appointedDate: String
  discount: Float
  status: String
}

input CreateUserInput {
  name: String!
  email: String!
  password: String!
  role: String!
}

input UpdateUserInput {
  id: String!
  name: String
  password: String
  role: String
}

input SignInInput {
  email: String!
  password: String!
}

input CreatePaymentAccountSettingInput {
  bankName: String!
  apiId: String
  apiKey: String
  checkSum: String
  isActive: Boolean!
  clientId: String!
  name: String
  mBankId: String
  bankOwnerName: String
  bankAccountNumber: String
  type: String!
}

input UpdatePaymentAccountSettingInput {
  bankName: String!
  apiId: String
  apiKey: String
  checkSum: String
  isActive: Boolean!
  clientId: String!
  name: String
  mBankId: String
  bankOwnerName: String
  bankAccountNumber: String
  type: String!
  id: ID!
}

input AdminCreateImageInput {
  orderId: String
}

"""The `Upload` scalar type represents a file upload."""
scalar Upload

input AdminReupImageInput {
  machineId: String!
  orderId: String!
}

input ClientSignInInput {
  email: String!
  password: String!
}

input GetClientAccountInput {
  name: String
  email: String
}

input CreateClientAccountInput {
  name: String!
  email: String!
  password: String!
}

input UpdateClientAccountInput {
  id: String!
  name: String
  password: String
  email: String
}

input ClientGetMachineInput {
  machineId: String
  machineCode: String
  status: String
  havePayScreen: Boolean
  haveOrder: Boolean
  userId: String
  location: String
}

input ClientUpdateMachineInput {
  id: String!
  location: String
}

input CreateWaitingScreenInput {
  name: String
  machineIds: String
}

input UpdateWaitingScreenInput {
  id: String!
  name: String
  machineIds: String
  oldImages: [ImageInput!]
}

input ImageInput {
  id: String!
  url: String
  isDeleted: Boolean
}

input CreateTopicInput {
  name: String!
  extraFee: Float! = 0
}

input UpdateTopicInput {
  id: String!
  name: String
  extraFee: Float
  isActive: Boolean
}

input CreateSettingSizeInput {
  name: String
  settingSizeType: String!
  machineIds: String
  smallSizePrice2: Int
  smallSizePrice4: Int
  smallSizePrice6: Int
  smallSizePrice8: Int
  smallSizePrice10: Int
  largeSizePrice2: Int
  largeSizePrice3: Int
  largeSizePrice4: Int
  largeSizePrice5: Int
  largeSizePrice6: Int
  status: String
}

input UpdateSettingSizeInput {
  id: String!
  name: String
  machineIds: String
  smallSizePrice2: Int
  smallSizePrice4: Int
  smallSizePrice6: Int
  smallSizePrice8: Int
  smallSizePrice10: Int
  largeSizePrice2: Int
  largeSizePrice3: Int
  largeSizePrice4: Int
  largeSizePrice5: Int
  largeSizePrice6: Int
  status: String
}

input AppearanceSettingInput {
  primary_color: String
  secondary_color: String
  background_color: String
  primary_text_color: String
  secondary_text_color: String
  secondary_text_color_2: String
}

input UpdateAppearanceSettingInput {
  id: String!
  primary_color: String
  secondary_color: String
  background_color: String
  primary_text_color: String
  secondary_text_color: String
  removeLogo: Boolean
  removeBackgroundImg: Boolean
  secondary_text_color_2: String
}

input UploadBackgroundImageInput {
  description: String
}

input CreateLayoutInput {
  name: String!
  machineIds: String
  layoutType: String!
  status: String!
}

input UpdateLayoutInput {
  id: String!
  name: String
  machineIds: String
  status: String
  topicId: String
}

input FrameInput {
  description: String
  imageUrl: String
  frameSize: String!
  numberImage: Float!
  numberPicture: Float!
  topicId: String!
  orientation: String!
  dateColor: String
  frameItems: [BaseItemInput!]!
}

input BaseItemInput {
  itemType: String!
  x_coordinate: Float!
  y_coordinate: Float!
  width: Float!
  height: Float!
  parentId: String
  itemId: String
  position: Float! = 0
  angle: Float
}

input UpdateFrameInput {
  description: String
  imageUrl: String
  frameSize: String
  numberImage: Float
  numberPicture: Float
  topicId: String
  orientation: String
  dateColor: String
  id: String!
  frameItems: [UpdateItemInput!]!
}

input UpdateItemInput {
  itemType: String
  x_coordinate: Float
  y_coordinate: Float
  width: Float
  height: Float
  parentId: String
  itemId: String
  position: Float = 0
  angle: Float
  id: String!
  isDeleted: Boolean
}

input FrameItemInput {
  itemType: String!
  frameId: String!
  x_coordinate: Float!
  y_coordinate: Float!
  width: Float!
  height: Float!
  parentId: String
  angle: Float
  itemId: String
}

input UpdateFrameItemInput {
  id: String!
  x_coordinate: Float
  y_coordinate: Float
  width: Float
  height: Float
  isDeleted: Boolean
  itemId: String
  angle: Float
}

input UpdateLayoutItemInput {
  id: String!
  imageUrl: String
  topicId: String
  position: Float
}

input CreatePromotionInput {
  name: String!
  startDate: String!
  endDate: String!
  usageLimitPerCode: Float!
  maxPromotionCodes: Float!
  machineIds: String
  discountValue: Float
}

input PrintImageInput {
  imageUrl: String!
  machineId: String!
  numberPrint: Float!
  size: String!
}

input CreatePrintSettingInput {
  machineId: String
  type: String
  adjustColor: Float
  printRetry: Float
  border: Float
  sharpness: Float
  overcoatFinish: Float
  printerQuality: Float
  yResolution: Float
  adjGammaR: Float
  adjGammaG: Float
  adjGammaB: Float
  adjBrightnessR: Float
  adjBrightnessG: Float
  adjBrightnessB: Float
  adjContrastR: Float
  adjContrastG: Float
  adjContrastB: Float
  adjChroma: Float
  icmMethod: Float
}

input UpdatePrintSettingInput {
  machineId: String
  type: String
  adjustColor: Float
  printRetry: Float
  border: Float
  sharpness: Float
  overcoatFinish: Float
  printerQuality: Float
  yResolution: Float
  adjGammaR: Float
  adjGammaG: Float
  adjGammaB: Float
  adjBrightnessR: Float
  adjBrightnessG: Float
  adjBrightnessB: Float
  adjContrastR: Float
  adjContrastG: Float
  adjContrastB: Float
  adjChroma: Float
  icmMethod: Float
  id: String!
}

input ReupImageInput {
  machineId: String!
  orderId: String!
}

input GenerateReconciliationCodeInput {
  month: String!
  amount: Float!
}

input SignInByCodeInput {
  machineCode: String!
  machinePin: String!
}

input AppOrderOnlineInput {
  promotionCode: String
  settingSizeId: String!
  settingSizeKey: String!
  frameId: String!
  topicId: String!
}

input AppOrderOnlineCashInput {
  promotionCode: String
  settingSizeId: String!
  settingSizeKey: String!
  frameId: String!
  topicId: String!
  receivedAmount: Float
  denominations: String
}

input ClientAppUpdateMachineInput {
  pendingPrints: Float
  remainingMedia: Float!
  remainingInk: Float!
  state: String!
}

input LogPublisherInput {
  """The log message to be recorded"""
  message: String!

  """The backtrace or stack trace related to the log"""
  backtrace: String
}

input CreateImageInput {
  orderId: String
  captureMode: String
}