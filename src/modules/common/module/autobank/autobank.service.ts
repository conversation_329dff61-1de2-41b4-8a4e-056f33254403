import { Injectable, Optional } from '@nestjs/common';
import 'dotenv/config';
import { SBLogger } from '../../../logger/logger.service';
import { encodeBase62 } from '../cryptoUtil/cryptoUtil.service';
@Injectable()
export class AutobankService {
  constructor(@Optional() private loggerService?: SBLogger) {}

  private generateUniqueCode(length: number = 4): string {
    const characters =
      'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      result += characters[randomIndex];
    }
    return result;
  }

  public async makePayment(
    machineId: string,
    amount: number,
    clientId: string,
    bankAccountNumber: string,
    bankOwnerName: string,
    bankCode: string,
  ) {
    // Temporary not used for order payment
    // const encodedClientId = encodeBase62(
    //   Buffer.from(clientId.replace(/-/g, ''), 'hex'),
    // );
    const encodedMachineId = encodeBase62(
      Buffer.from(machineId.replace(/-/g, ''), 'hex'),
    );
    const uniqueCode = this.generateUniqueCode();
    const refCode = `SPS ${encodedMachineId} ${uniqueCode}SPE`;
    try {
      const qrCodeUrl = `https://img.vietqr.io/image/${bankCode}-${bankAccountNumber}-compact2.png?amount=${amount}&addInfo=${encodeURIComponent(refCode)}&accountName=${encodeURIComponent(bankOwnerName)}`;

      return {
        refCode,
        qrCodeUrl,
      };
    } catch (error) {
      console.log('ERROR - autobank - makePayment', error);
    }
  }

  public async makeReconciliationPayment(
    clientId: string,
    amount: number,
    bankAccountNumber: string,
    bankOwnerName: string,
    bankCode: string,
  ) {
    const encodedClientId = encodeBase62(
      Buffer.from(clientId.replace(/-/g, ''), 'hex'),
    );
    const uniqueCode = this.generateUniqueCode();
    const refCode = `SPS ${encodedClientId} ${uniqueCode}SPE`;
    try {
      const qrCodeUrl = `https://img.vietqr.io/image/${bankCode}-${bankAccountNumber}-compact2.png?amount=${amount}&addInfo=${encodeURIComponent(refCode)}&accountName=${encodeURIComponent(bankOwnerName)}`;

      return {
        refCode,
        qrCodeUrl,
      };
    } catch (error) {
      console.log('ERROR - autobank - makeReconciliationPayment', error);
    }
  }
}
