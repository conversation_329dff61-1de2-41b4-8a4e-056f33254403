import { Module } from '@nestjs/common';
import { AutobankService } from './autobank.service';
import { AutobankController } from './autobank.controller';
import { Machine } from '../common/entity/machine.entity';
import { Order } from '../clientApp/order/order.entity';
import { Promotion } from '../common/entity/promotion.entity';
import { PromotionCode } from '../common/entity/promotionCode.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FirestoreService } from '../common/module/firestore/firestore.service';
import { PromotionModule } from '../common/module/promotion/promotion.module';
import { PaymentAccountSetting } from '../common/entity/paymentAccountSetting.entity';
import { CostReconciliationService } from '../client/costReconciliation/costReconciliation.service';
import { CostReconciliation } from '../common/entity/costReconciliation.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Machine,
      Order,
      Promotion,
      PromotionCode,
      PaymentAccountSetting,
      CostReconciliation,
    ]),
    PromotionModule,
  ],
  controllers: [AutobankController],
  providers: [AutobankService, FirestoreService, CostReconciliationService],
})
export class AutobankModule {}
