import { Resolver, Query, Args, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { CostReconciliationService } from './costReconciliation.service';
import {
  GetCostReconciliationInput,
  CostReconciliationListResponse,
  GetReconciliationCodeByMonthInput,
  ReconciliationCodeResponse,
} from './costReconciliation.input';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';

@Resolver()
export class CostReconciliationResolver {
  constructor(private costReconciliationService: CostReconciliationService) {}

  // Cost Reconciliation Resolvers
  @Query(() => CostReconciliationListResponse)
  @UseGuards(ClientAuthGuard)
  async clientCostReconciliations(
    @Args('input') input: GetCostReconciliationInput,
    @Context() context: any,
  ): Promise<CostReconciliationListResponse> {
    // Auto-fill clientId from context for client users
    const clientId = context.req.user.id;
    const inputWithClientId = { ...input, clientId };

    return this.costReconciliationService.getCostReconciliations(
      inputWithClientId,
    );
  }

  // Get reconciliation code by month
  @Query(() => ReconciliationCodeResponse, { nullable: true })
  @UseGuards(ClientAuthGuard)
  async getReconciliationCodeByMonth(
    @Args('input') input: GetReconciliationCodeByMonthInput,
    @Context() context: any,
  ): Promise<ReconciliationCodeResponse | null> {
    const clientId = context.req.user.id;
    return this.costReconciliationService.getReconciliationCodeByMonth(
      clientId,
      input.month,
    );
  }
}
