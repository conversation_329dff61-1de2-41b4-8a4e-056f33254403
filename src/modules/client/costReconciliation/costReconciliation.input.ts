import {
  Field,
  InputType,
  ObjectType,
  Int,
  registerEnumType,
} from '@nestjs/graphql';
import { ECostReconciliationStatus } from '../../../enum';

registerEnumType(ECostReconciliationStatus, {
  name: 'ECostReconciliationStatus',
});

// Cost Reconciliation DTOs
@InputType()
export class GetCostReconciliationInput {
  @Field({ nullable: true })
  clientId?: string;

  @Field({ nullable: true })
  status?: ECostReconciliationStatus;

  @Field({ nullable: true })
  startDate?: number; // Unix timestamp

  @Field({ nullable: true })
  endDate?: number; // Unix timestamp

  @Field(() => Int, { nullable: true, defaultValue: 1 })
  page?: number;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  limit?: number;
}

@ObjectType()
export class CostReconciliationResponse {
  @Field()
  id: string;

  @Field()
  clientId: string;

  @Field()
  clientName: string;

  @Field()
  revenue: number;

  @Field()
  discountAmount: number;

  @Field()
  status: ECostReconciliationStatus;

  @Field()
  reconciliationDate: Date;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;

  @Field({ nullable: true })
  createdBy?: string;

  @Field({ nullable: true })
  updatedBy?: string;

  @Field({ nullable: true })
  notes?: string;

  @Field(() => Int)
  totalOrders: number;

  @Field({ nullable: true })
  processedOrderIds?: string;

  @Field()
  reconciliationCode: string;
}

@ObjectType()
export class CostReconciliationListResponse {
  @Field(() => [CostReconciliationResponse])
  data: CostReconciliationResponse[];

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  totalPages: number;
}

// Input for getting reconciliation code by month
@InputType()
export class GetReconciliationCodeByMonthInput {
  @Field()
  month: string; // Format: YYYY-MM

  @Field({ nullable: true })
  clientId?: string; // Optional for admin, auto-filled for client
}

// Response for reconciliation code
@ObjectType()
export class ReconciliationCodeResponse {
  @Field()
  refCode: string;

  @Field({ nullable: true })
  qrCodeUrl?: string;

  @Field()
  month: string;

  @Field()
  clientId: string;

  @Field()
  clientName: string;

  @Field()
  revenue: number;

  @Field()
  discountAmount: number;

  @Field()
  totalOrders: number;

  @Field()
  status: ECostReconciliationStatus;

  @Field()
  generatedAt: Date;
}
