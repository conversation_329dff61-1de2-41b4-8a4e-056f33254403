import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CostReconciliation } from '../../common/entity/costReconciliation.entity';
import { AutobankService } from '../../common/module/autobank/autobank.service';
import { PaymentAccountSetting } from '../../common/entity/paymentAccountSetting.entity';
import { ECostReconciliationStatus } from '../../../enum/index';

import {
  GetCostReconciliationInput,
  CostReconciliationResponse,
  CostReconciliationListResponse,
} from './costReconciliation.input';

import { convertDateRangeFromUnix } from '../../../utils/time.utils';
import {
  generateReconciliationCode,
  parseOrderIds,
  stringifyOrderIds,
  mergeOrderIds,
  calculateDiscountAmount,
  createMonthKeyFromDate,
} from '../../../utils/reconciliation.util';

@Injectable()
export class CostReconciliationService {
  constructor(
    @InjectRepository(CostReconciliation)
    private costReconciliationRepository: Repository<CostReconciliation>,
    @InjectRepository(PaymentAccountSetting)
    private paymentAccountSettingRepository: Repository<PaymentAccountSetting>,
    private autobankService: AutobankService,
  ) {}

  async getCostReconciliations(
    input: GetCostReconciliationInput,
  ): Promise<CostReconciliationListResponse> {
    const {
      clientId,
      status,
      startDate,
      endDate,
      page = 1,
      limit = 10,
    } = input;

    console.log(input);

    const queryBuilder = this.costReconciliationRepository
      .createQueryBuilder('cr')
      .leftJoinAndSelect('cr.client', 'client');

    if (clientId) {
      queryBuilder.andWhere('cr.clientId = :clientId', { clientId });
    }

    if (status) {
      queryBuilder.andWhere('cr.status = :status', { status });
    }

    if (startDate && endDate) {
      const { startDate: startDateObj, endDate: endDateObj } =
        convertDateRangeFromUnix(startDate.toString(), endDate.toString());
      queryBuilder.andWhere(
        'cr.reconciliationDate BETWEEN :startDate AND :endDate',
        { startDate: startDateObj, endDate: endDateObj },
      );
    }

    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);
    queryBuilder.orderBy('cr.createdAt', 'DESC');

    const [costReconciliations, total] = await queryBuilder.getManyAndCount();

    const data: CostReconciliationResponse[] = costReconciliations.map(
      (cr) => ({
        id: cr.id,
        clientId: cr.clientId,
        clientName: cr.client?.name || '',
        revenue: cr.revenue,
        discountAmount: cr.discountAmount,
        status: cr.status,
        reconciliationDate: cr.reconciliationDate,
        createdAt: cr.createdAt,
        updatedAt: cr.updatedAt,
        createdBy: cr.createdBy,
        updatedBy: cr.updatedBy,
        notes: cr.notes,
        totalOrders: cr.totalOrders,
        processedOrderIds: cr.processedOrderIds,
        reconciliationCode: cr.reconciliationCode,
      }),
    );

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async generateUniqueReconciliationCode(): Promise<string> {
    let code: string;
    let isUnique = false;
    let attempts = 0;
    const maxAttempts = 10;

    while (!isUnique && attempts < maxAttempts) {
      code = generateReconciliationCode();
      const existingRecord = await this.costReconciliationRepository.findOne({
        where: { reconciliationCode: code },
      });
      if (!existingRecord) {
        isUnique = true;
      }
      attempts++;
    }

    if (!isUnique) {
      throw new Error(
        'Failed to generate unique reconciliation code after maximum attempts',
      );
    }

    return code;
  }

  async findOrCreateMonthlyReconciliation(
    clientId: string,
    orderIds: string[],
    revenue: number,
    month: string,
  ): Promise<CostReconciliationResponse> {
    const [year, monthNum] = month.split('-').map(Number);
    const monthDate = new Date(year, monthNum - 1, 1);

    const existingReconciliation = await this.costReconciliationRepository
      .createQueryBuilder('cr')
      .where('cr.clientId = :clientId', { clientId })
      .andWhere('cr.reconciliationDate = :monthDate', { monthDate })
      .getOne();

    if (existingReconciliation) {
      return this.addOrdersToExistingReconciliation(
        existingReconciliation,
        orderIds,
        revenue,
      );
    } else {
      return this.createNewMonthlyReconciliation(
        clientId,
        orderIds,
        revenue,
        monthDate,
      );
    }
  }

  private async addOrdersToExistingReconciliation(
    existingRecord: CostReconciliation,
    newOrderIds: string[],
    newRevenue: number,
  ): Promise<CostReconciliationResponse> {
    const existingOrderIds = parseOrderIds(existingRecord.processedOrderIds);
    const mergedOrderIds = mergeOrderIds(existingOrderIds, newOrderIds);
    const existingRevenueNum = Number(existingRecord.revenue);
    const newRevenueNum = Number(newRevenue);
    const updatedRevenue = existingRevenueNum + newRevenueNum;
    const updatedTotalOrders = mergedOrderIds.length;
    const updatedDiscountAmount = calculateDiscountAmount(
      updatedRevenue,
      updatedTotalOrders,
    );

    existingRecord.processedOrderIds = stringifyOrderIds(mergedOrderIds);
    existingRecord.revenue = updatedRevenue;
    existingRecord.totalOrders = updatedTotalOrders;
    existingRecord.discountAmount = updatedDiscountAmount;
    existingRecord.updatedBy = 'system';

    const monthYear = `${existingRecord.reconciliationDate.getFullYear()}-${String(
      existingRecord.reconciliationDate.getMonth() + 1,
    ).padStart(2, '0')}`;
    existingRecord.notes = `Auto-generated monthly reconciliation for ${monthYear} (${updatedTotalOrders} orders, updated: ${
      new Date().toISOString().split('T')[0]
    })`;

    const savedRecord =
      await this.costReconciliationRepository.save(existingRecord);

    const recordWithClient = await this.costReconciliationRepository.findOne({
      where: { id: savedRecord.id },
      relations: ['client'],
    });

    return {
      id: recordWithClient.id,
      clientId: recordWithClient.clientId,
      clientName: recordWithClient.client?.name || '',
      revenue: recordWithClient.revenue,
      discountAmount: recordWithClient.discountAmount,
      status: recordWithClient.status,
      reconciliationDate: recordWithClient.reconciliationDate,
      createdAt: recordWithClient.createdAt,
      updatedAt: recordWithClient.updatedAt,
      createdBy: recordWithClient.createdBy,
      updatedBy: recordWithClient.updatedBy,
      notes: recordWithClient.notes,
      totalOrders: recordWithClient.totalOrders,
      processedOrderIds: recordWithClient.processedOrderIds,
      reconciliationCode: recordWithClient.reconciliationCode,
    };
  }

  private async createNewMonthlyReconciliation(
    clientId: string,
    orderIds: string[],
    revenue: number,
    reconciliationDate: Date,
  ): Promise<CostReconciliationResponse> {
    const reconciliationCode = await this.generateUniqueReconciliationCode();
    const totalOrders = orderIds.length;
    const discountAmount = calculateDiscountAmount(revenue, totalOrders);

    const newRecord = this.costReconciliationRepository.create({
      clientId,
      revenue,
      discountAmount,
      status: ECostReconciliationStatus.NOT_RECONCILED,
      processedOrderIds: stringifyOrderIds(orderIds),
      totalOrders,
      reconciliationDate,
      reconciliationCode,
      createdBy: 'system',
      notes: `Auto-generated monthly reconciliation for ${reconciliationDate.getFullYear()}-${String(
        reconciliationDate.getMonth() + 1,
      ).padStart(2, '0')} (${totalOrders} orders)`,
    });

    const savedRecord = await this.costReconciliationRepository.save(newRecord);

    const recordWithClient = await this.costReconciliationRepository.findOne({
      where: { id: savedRecord.id },
      relations: ['client'],
    });

    return {
      id: recordWithClient.id,
      clientId: recordWithClient.clientId,
      clientName: recordWithClient.client?.name || '',
      revenue: recordWithClient.revenue,
      discountAmount: recordWithClient.discountAmount,
      status: recordWithClient.status,
      reconciliationDate: recordWithClient.reconciliationDate,
      createdAt: recordWithClient.createdAt,
      updatedAt: recordWithClient.updatedAt,
      createdBy: recordWithClient.createdBy,
      updatedBy: recordWithClient.updatedBy,
      notes: recordWithClient.notes,
      totalOrders: recordWithClient.totalOrders,
      processedOrderIds: recordWithClient.processedOrderIds,
      reconciliationCode: recordWithClient.reconciliationCode,
    };
  }

  async processOrderForReconciliation(
    clientId: string,
    orderId: string,
    revenue: number,
    orderDate: Date,
  ): Promise<CostReconciliationResponse> {
    const monthKey = createMonthKeyFromDate(orderDate);
    const result = await this.findOrCreateMonthlyReconciliation(
      clientId,
      [orderId],
      revenue,
      monthKey,
    );
    return result;
  }

  async getReconciliationCodeByMonth(
    clientId: string,
    month: string,
  ): Promise<any> {
    const [year, monthNum] = month.split('-').map(Number);
    const monthDate = new Date(year, monthNum - 1, 1);

    const reconciliation = await this.costReconciliationRepository
      .createQueryBuilder('cr')
      .leftJoinAndSelect('cr.client', 'client')
      .where('cr.clientId = :clientId', { clientId })
      .andWhere('cr.reconciliationDate = :monthDate', { monthDate })
      .getOne();

    if (!reconciliation) {
      return null;
    }

    // Get payment account setting for this client
    const paymentSetting = await this.paymentAccountSettingRepository
      .createQueryBuilder('pas')
      .leftJoinAndSelect('pas.mBank', 'mBank')
      .where('pas.clientId = :clientId', { clientId })
      .andWhere('pas.isActive = :isActive', { isActive: true })
      .getOne();

    if (!paymentSetting) {
      // Return basic info without payment QR if no payment setting
      return {
        refCode: reconciliation.reconciliationCode,
        month,
        clientId: reconciliation.clientId,
        clientName: reconciliation.client?.name || '',
        revenue: reconciliation.revenue,
        discountAmount: reconciliation.discountAmount,
        totalOrders: reconciliation.totalOrders,
        status: reconciliation.status,
        generatedAt: reconciliation.createdAt,
      };
    }

    // Generate payment QR using AutobankService with encodedClientId
    const paymentResult = await this.autobankService.makeReconciliationPayment(
      clientId,
      reconciliation.discountAmount,
      paymentSetting.bankAccountNumber,
      paymentSetting.bankOwnerName,
      paymentSetting.mBank.code,
    );

    return {
      refCode: paymentResult?.refCode || reconciliation.reconciliationCode,
      qrCodeUrl: paymentResult?.qrCodeUrl,
      month,
      clientId: reconciliation.clientId,
      clientName: reconciliation.client?.name || '',
      revenue: reconciliation.revenue,
      discountAmount: reconciliation.discountAmount,
      totalOrders: reconciliation.totalOrders,
      status: reconciliation.status,
      generatedAt: reconciliation.createdAt,
    };
  }
}
