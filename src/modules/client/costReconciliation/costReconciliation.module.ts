import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CostReconciliation } from '../../common/entity/costReconciliation.entity';
import { PaymentAccountSetting } from '../../common/entity/paymentAccountSetting.entity';
import { MBank } from '../../common/entity/mBank.entity';
import { CostReconciliationService } from './costReconciliation.service';
import { CostReconciliationResolver } from './costReconciliation.resolver';
import { CostReconciliationCodeService } from '../../common/module/costReconciliationCode/costReconciliationCode.service';
import { CostReconciliationCodeResolver } from './costReconciliationCode.resolver';
import { AutobankService } from '../../common/module/autobank/autobank.service';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CostReconciliation,
      PaymentAccountSetting,
      MBank,
    ]),
  ],
  providers: [
    CostReconciliationService,
    CostReconciliationResolver,
    CostReconciliationCodeService,
    CostReconciliationCodeResolver,
    AutobankService,
    JwtService,
    SBLogger,
  ],
  exports: [CostReconciliationService, CostReconciliationCodeService],
})
export class CostReconciliationModule {}
