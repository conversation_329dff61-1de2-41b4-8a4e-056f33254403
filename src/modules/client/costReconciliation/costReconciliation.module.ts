import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CostReconciliation } from '../../common/entity/costReconciliation.entity';
import { PaymentAccountSetting } from '../../common/entity/paymentAccountSetting.entity';
import { MBank } from '../../common/entity/mBank.entity';
import { CostReconciliationService } from './costReconciliation.service';
import { CostReconciliationResolver } from './costReconciliation.resolver';
import { CostReconciliationCodeService } from '../../common/module/costReconciliationCode/costReconciliationCode.service';
import { CostReconciliationCodeResolver } from './costReconciliationCode.resolver';
import { CommonAutobankModule } from '../../common/module/autobank/autobank.module';
import { AutobankModule } from '../../autobank/autobank.module';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CostReconciliation,
      PaymentAccountSetting,
      MBank,
    ]),
    CommonAutobankModule,
    forwardRef(() => AutobankModule),
  ],
  providers: [
    CostReconciliationService,
    CostReconciliationResolver,
    CostReconciliationCodeService,
    CostReconciliationCodeResolver,
    JwtService,
    SBLogger,
  ],
  exports: [CostReconciliationService, CostReconciliationCodeService],
})
export class CostReconciliationModule {}
