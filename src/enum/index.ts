export enum EMachineStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export enum EProductionStatus {
  IN_PRODUCTION = 'IN_PRODUCTION',
  COMPLETED = 'COMPLETED',
  DELIVERED = 'DELIVERED',
}

export enum EOrderAppStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  DELIVERED = 'DELIVERED',
}

export enum EPaymentMethod {
  ONLINE = 'ONLINE',
  OFFLINE = 'OFFLINE',
}

export enum EOrderStatus {
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELED = 'CANCELED',
}

export enum EWaitingScreenType {
  Default = 'Default',
  Custom = 'Custom',
}

export enum ESettingSizeType {
  Default = 'Default',
  Custom = 'Custom',
}

export enum ELayoutStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export enum ELayoutType {
  Default = 'Default',
  Custom = 'Custom',
}

export enum EFrameSize {
  Big = 'Big',
  Small = 'Small',
}

export enum EFrameItemType {
  SubFrame = 'SubFrame',
  DubFrame = 'DubFrame',
  QrCode = 'QrCode',
  Date = 'Date',
}

export enum EFrameOrientation {
  Horizontal = 'Horizontal',
  Vertical = 'Vertical',
}

export enum ESettingSizeStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export enum ELayoutFormatStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export enum ECaptureMode {
  AUTO = 'AUTO',
  MANUAL = 'MANUAL',
}

export enum EMachineStatus {
  WAITING = 'WAITING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}

export enum EImageFileType {
  IMAGE = 'IMAGE',
  IMAGE_FINAL = 'IMAGE_FINAL',
  VIDEO = 'VIDEO',
}

export enum EPrintSettingType {
  Default = 'Default',
  Custom = 'Custom',
}

export enum ECostReconciliationStatus {
  PAID = 'PAID',
  UNPAID = 'UNPAID',
  NOT_RECONCILED = 'NOT_RECONCILED',
}

export enum EPaymentProviderType {
  AUTOBANK = 'AUTOBANK',
  PAYOS = 'PAYOS',
  OTHER = 'OTHER',
}
