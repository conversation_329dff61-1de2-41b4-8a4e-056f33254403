const { publicEncrypt, constants } = require('crypto');

// ======== HARD-CODED PUBLIC KEY ========
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

// ======== DATA TO ENCRYPT ========
const payload = {
  refCode: 'SPS 6AeiwDZVaRqodF6SfVQcJa pX1sSPE',
  amount: 140000,
};

// ======== ENCRYPT ========
try {
  const bufferData = Buffer.from(JSON.stringify(payload), 'utf-8');

  const encrypted = publicEncrypt(
    {
      key: publicKey,
      padding: constants.RSA_PKCS1_OAEP_PADDING,
    },
    bufferData,
  );

  const base64Encoded = encrypted.toString('base64');
  console.log('🔐 Encrypted base64:\n');
  console.log(base64Encoded);
} catch (err) {
  console.error('❌ Encryption failed:', err);
}




// curl -X POST \
//   http://localhost:3001/autobank/callback \
//   -H "Content-Type: application/json" \
//   -d '{
//     "data": "D7yvO7mzoDtkbLE+M/0XxCQzQpMmnC6yiWSjtPVALJvYbq57OQ35wddkFHGwzLxgy819QFPz6CVzUJyclnNkH4d6tr9KfYSkmjAF0DVQ2yIXpCLVMn/FfoFxO+1mm74cNc9VzN5I806EA1wk1nF0UtOdgPYTzjV2mok7vH8tjjUhpfVvY8SgiH1Wk3q/o3X6rOVKYpgwa0XhEN66rBhwPPh7f7GbaUT2LtpUZyQIYIW1NdcjdUA6Ls7cI4qBrR6fzPCy+u0rHwOFzriXKhLqeEtrKSHPNb1SSE/kUgjqAjRn7wrtVZCAz3ylxVDwIMUqu9+acifO1kVL5HbUWcwctA=="
//   }'